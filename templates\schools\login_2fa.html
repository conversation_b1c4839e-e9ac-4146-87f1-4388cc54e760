{% extends 'schools/base.html' %}
{% load static %}

{% block title %}Two-Factor Authentication - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-lg border-0 rounded-lg mt-5">
                <div class="card-header">
                    <h3 class="text-center font-weight-light my-4">
                        <i class="fas fa-shield-alt text-primary"></i>
                        Two-Factor Authentication
                    </h3>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <p class="text-muted">
                            Hello, <strong>{{ user.get_full_name }}</strong>
                        </p>
                        <p class="text-muted">
                            Please enter the 6-digit code from your authenticator app to complete your login.
                        </p>
                    </div>

                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        <div class="form-floating mb-3">
                            <input 
                                class="form-control" 
                                id="totp_token" 
                                name="totp_token" 
                                type="text" 
                                placeholder="000000"
                                maxlength="6"
                                pattern="[0-9]{6}"
                                autocomplete="one-time-code"
                                required
                                autofocus
                            />
                            <label for="totp_token">
                                <i class="fas fa-key"></i> Authentication Code
                            </label>
                        </div>

                        <div class="d-grid">
                            <button class="btn btn-primary btn-block" type="submit">
                                <i class="fas fa-sign-in-alt"></i> Verify & Login
                            </button>
                        </div>
                    </form>

                    <div class="text-center mt-4">
                        <a href="{% url 'schools:staff_login' %}" class="text-muted me-3">
                            <i class="fas fa-arrow-left"></i> Back to Login
                        </a>
                        <span class="text-muted">|</span>
                        <a href="{% url 'schools:staff_logout_view' %}" class="text-muted ms-3">
                            <i class="fas fa-sign-out-alt"></i> Cancel & Logout
                        </a>
                    </div>
                </div>
                <div class="card-footer text-center py-3">
                    <div class="small">
                        <i class="fas fa-info-circle text-info"></i>
                        Open your authenticator app (Google Authenticator, Authy, etc.) to get your 6-digit code.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-focus and auto-submit when 6 digits are entered
document.getElementById('totp_token').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
    e.target.value = value;
    
    if (value.length === 6) {
        // Auto-submit when 6 digits are entered
        setTimeout(() => {
            e.target.form.submit();
        }, 100);
    }
});

// Format input as user types
document.getElementById('totp_token').addEventListener('keyup', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length <= 6) {
        e.target.value = value;
    }
});
</script>
{% endblock %}
