{% extends "tenant_base.html" %}
{% load static i18n %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">2FA Settings</li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-shield-check me-2"></i>Two-Factor Authentication Settings
                    </h4>
                </div>
                <div class="card-body">
                    {% include "partials/_messages.html" %}
                    
                    {% if has_2fa %}
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <strong>2FA is active</strong> - Your account is protected with two-factor authentication.
                        </div>
                        
                        <h5>Active Devices</h5>
                        {% for device in devices %}
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">
                                                <i class="bi bi-phone me-2"></i>Authenticator App
                                            </h6>
                                            <small class="text-muted">Device: {{ device.name|title }}</small>
                                        </div>
                                        <span class="badge bg-success">Active</span>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                        
                        <div class="mt-4">
                            <h5>Security Actions</h5>
                            <div class="d-grid gap-2 d-md-block">
                                <form method="post" action="{% url 'schools:disable_2fa' %}" class="d-inline">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-outline-danger" 
                                            onclick="return confirm('Are you sure you want to disable 2FA? This will make your account less secure and you will need to set it up again if you want to re-enable it.')">
                                        <i class="bi bi-shield-x me-2"></i>Disable Two-Factor Authentication
                                    </button>
                                </form>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h6><i class="bi bi-info-circle me-2"></i>Security Tips</h6>
                                <ul class="mb-0">
                                    <li>Keep your authenticator app updated</li>
                                    <li>Back up your authenticator app data</li>
                                    <li>Don't share your verification codes with anyone</li>
                                    <li>If you lose your device, contact your administrator immediately</li>
                                </ul>
                            </div>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <strong>2FA is not enabled</strong> - Your account could be more secure.
                        </div>
                        
                        <p>Two-factor authentication is not currently enabled for your account.</p>
                        
                        <a href="{% url 'schools:setup_2fa' %}" class="btn btn-primary">
                            <i class="bi bi-shield-plus me-2"></i>Enable Two-Factor Authentication
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
