# Two-Factor Authentication Views for Staff Users
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django_otp.plugins.otp_totp.models import TOTPDevice
from django_otp.util import random_hex
from django.conf import settings
import qrcode
import io
import base64


@login_required
def setup_2fa(request):
    """Setup 2FA for the current staff user"""
    # Simple fix: Get the user from the session if request.user is problematic
    from apps.schools.models import StaffUser
    from django.contrib.auth import get_user

    # Try to get the actual user object
    try:
        if hasattr(request.user, 'pk') and request.user.pk:
            # User object is fine
            user = request.user
        else:
            # Try to get user from session
            user_id = request.session.get('_auth_user_id')
            if user_id:
                user = StaffUser.objects.get(pk=user_id)
            else:
                messages.error(request, 'Unable to identify user. Please log in again.')
                return redirect('schools:staff_login')
    except (StaffUser.DoesNotExist, AttributeError, ValueError):
        messages.error(request, 'Unable to identify user. Please log in again.')
        return redirect('schools:staff_login')

    if request.method == 'POST':
        # Create or get existing TOTP device
        device, created = TOTPDevice.objects.get_or_create(
            user=user,
            name='default',
            defaults={'confirmed': False}
        )
        
        if not created and device.confirmed:
            messages.warning(request, '2FA is already enabled for your account.')
            return redirect('schools:profile_2fa')
        
        # Generate QR code
        qr_url = device.config_url
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(qr_url)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        buffer.seek(0)
        qr_code_data = base64.b64encode(buffer.getvalue()).decode()
        
        context = {
            'device': device,
            'qr_code_data': qr_code_data,
            'secret_key': device.key,
            'view_title': 'Setup Two-Factor Authentication'
        }
        return render(request, 'schools/2fa_setup.html', context)
    
    # Check if user already has 2FA enabled
    has_2fa = TOTPDevice.objects.filter(user=user, confirmed=True).exists()
    
    context = {
        'has_2fa': has_2fa,
        'view_title': 'Two-Factor Authentication'
    }
    return render(request, 'schools/2fa_status.html', context)


@login_required
@require_POST
def confirm_2fa(request):
    """Confirm 2FA setup with verification code"""
    # Get the actual user object
    from apps.schools.models import StaffUser
    try:
        if hasattr(request.user, 'pk') and request.user.pk:
            user = request.user
        else:
            user_id = request.session.get('_auth_user_id')
            user = StaffUser.objects.get(pk=user_id) if user_id else None
            if not user:
                messages.error(request, 'Unable to identify user. Please log in again.')
                return redirect('schools:staff_login')
    except (StaffUser.DoesNotExist, AttributeError, ValueError):
        messages.error(request, 'Unable to identify user. Please log in again.')
        return redirect('schools:staff_login')

    token = request.POST.get('token', '').strip()

    if not token:
        messages.error(request, 'Please enter the verification code.')
        return redirect('schools:setup_2fa')

    # Get the unconfirmed device
    try:
        device = TOTPDevice.objects.get(user=user, name='default', confirmed=False)
    except TOTPDevice.DoesNotExist:
        messages.error(request, 'No 2FA setup found. Please start the setup process again.')
        return redirect('schools:setup_2fa')
    
    # Verify the token
    if device.verify_token(token):
        device.confirmed = True
        device.save()
        messages.success(request, '2FA has been successfully enabled for your account!')
        return redirect('schools:profile_2fa')
    else:
        messages.error(request, 'Invalid verification code. Please try again.')
        return redirect('schools:setup_2fa')


@login_required
@require_POST
def disable_2fa(request):
    """Disable 2FA for the current user"""
    # Get the actual user object
    from apps.schools.models import StaffUser
    try:
        if hasattr(request.user, 'pk') and request.user.pk:
            user = request.user
        else:
            user_id = request.session.get('_auth_user_id')
            user = StaffUser.objects.get(pk=user_id) if user_id else None
            if not user:
                messages.error(request, 'Unable to identify user. Please log in again.')
                return redirect('schools:staff_login')
    except (StaffUser.DoesNotExist, AttributeError, ValueError):
        messages.error(request, 'Unable to identify user. Please log in again.')
        return redirect('schools:staff_login')

    # Delete all TOTP devices for the user
    deleted_count = TOTPDevice.objects.filter(user=user).delete()[0]
    
    if deleted_count > 0:
        messages.success(request, '2FA has been disabled for your account.')
    else:
        messages.info(request, 'No 2FA devices found to disable.')
    
    return redirect('schools:profile_2fa')


@login_required
def profile_2fa(request):
    """Show 2FA status and management options"""
    # Get the actual user object
    from apps.schools.models import StaffUser
    try:
        if hasattr(request.user, 'pk') and request.user.pk:
            user = request.user
        else:
            user_id = request.session.get('_auth_user_id')
            user = StaffUser.objects.get(pk=user_id) if user_id else None
            if not user:
                messages.error(request, 'Unable to identify user. Please log in again.')
                return redirect('schools:staff_login')
    except (StaffUser.DoesNotExist, AttributeError, ValueError):
        messages.error(request, 'Unable to identify user. Please log in again.')
        return redirect('schools:staff_login')

    devices = TOTPDevice.objects.filter(user=user, confirmed=True)
    has_2fa = devices.exists()

    context = {
        'has_2fa': has_2fa,
        'devices': devices,
        'view_title': 'Two-Factor Authentication Settings'
    }
    return render(request, 'schools/2fa_profile.html', context)
