# your_project/apps/schools/two_factor_views.py

import io
import base64
import qrcode
from django.views import View
from django.shortcuts import render, redirect
from django.contrib import messages
from django.urls import reverse_lazy
from django.contrib.auth.mixins import LoginRequiredMixin

# OTP Imports
from django_otp.plugins.otp_totp.models import TOTPDevice

class TwoFactorManagementView(LoginRequiredMixin, View):
    """
    A single, consolidated view to manage a user's 2FA status.
    - GET: Shows current status or starts the setup process.
    - POST: Verifies a new device to complete the setup.
    """
    template_name = 'schools/2fa_management.html'
    login_url = reverse_lazy('schools:staff_login') # For LoginRequiredMixin

    def get_user_device(self, user, confirmed=None):
        """Helper to get a user's TOTP device."""
        # Ensure we have a proper User object, not a string
        if isinstance(user, str):
            return None

        if not hasattr(user, 'pk') or not user.pk:
            return None

        # Use direct query instead of devices_for_user to avoid string issues
        try:
            if confirmed is None:
                return TOTPDevice.objects.filter(user=user).first()
            else:
                return TOTPDevice.objects.filter(user=user, confirmed=confirmed).first()
        except Exception:
            return None

    def get(self, request, *args, **kwargs):
        """
        Handles displaying the 2FA status or generating a new setup QR code.
        """
        # Handle the case where request.user might be a string representation
        from apps.schools.models import StaffUser
        try:
            if isinstance(request.user, str):
                # If it's a string, try to get user from session
                user_id = request.session.get('_auth_user_id')
                if user_id:
                    user = StaffUser.objects.get(pk=user_id)
                else:
                    messages.error(request, 'Session expired. Please log in again.')
                    return redirect('schools:staff_login')
            elif hasattr(request.user, 'pk') and request.user.pk:
                # User object is fine
                user = request.user
            else:
                # Try to get user from session as fallback
                user_id = request.session.get('_auth_user_id')
                if user_id:
                    user = StaffUser.objects.get(pk=user_id)
                else:
                    messages.error(request, 'Unable to identify user. Please log in again.')
                    return redirect('schools:staff_login')
        except (StaffUser.DoesNotExist, AttributeError, ValueError) as e:
            messages.error(request, f'Authentication error: {str(e)}. Please log in again.')
            return redirect('schools:staff_login')

        device = self.get_user_device(user, confirmed=True)
        if device:
            # 2FA is already enabled and confirmed. Show the status page.
            context = {
                'view_title': 'Two-Factor Authentication',
                'has_2fa': True,
                'device': device,
            }
            return render(request, self.template_name, context)

        # 2FA is not enabled. Begin the setup process.
        # Get or create an unconfirmed device.
        device = self.get_user_device(user, confirmed=False)
        if not device:
            device = TOTPDevice(user=user, name='default', confirmed=False)
            device.save()

        # Generate QR code for the unconfirmed device
        qr_url = device.config_url
        img = qrcode.make(qr_url)
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        qr_code_data = base64.b64encode(buffer.getvalue()).decode('utf-8')

        context = {
            'view_title': 'Setup Two-Factor Authentication',
            'has_2fa': False,
            'qr_code_data': qr_code_data,
            'secret_key': device.key,
        }
        return render(request, self.template_name, context)

    def post(self, request, *args, **kwargs):
        """
        Handles the form submission to verify and confirm a new 2FA device.
        """
        # Handle the case where request.user might be a string representation
        from apps.schools.models import StaffUser
        try:
            if isinstance(request.user, str):
                # If it's a string, try to get user from session
                user_id = request.session.get('_auth_user_id')
                if user_id:
                    user = StaffUser.objects.get(pk=user_id)
                else:
                    messages.error(request, 'Session expired. Please log in again.')
                    return redirect('schools:staff_login')
            elif hasattr(request.user, 'pk') and request.user.pk:
                # User object is fine
                user = request.user
            else:
                # Try to get user from session as fallback
                user_id = request.session.get('_auth_user_id')
                if user_id:
                    user = StaffUser.objects.get(pk=user_id)
                else:
                    messages.error(request, 'Unable to identify user. Please log in again.')
                    return redirect('schools:staff_login')
        except (StaffUser.DoesNotExist, AttributeError, ValueError) as e:
            messages.error(request, f'Authentication error: {str(e)}. Please log in again.')
            return redirect('schools:staff_login')
        token = request.POST.get('token', '').strip()
        
        if not token:
            messages.error(request, "Please enter the 6-digit verification code from your authenticator app.")
            return redirect('schools:profile_2fa')

        device = self.get_user_device(user, confirmed=False)
        
        if not device:
            messages.error(request, "Setup process has expired. Please try again.")
            return redirect('schools:profile_2fa')

        # Verify the token against the unconfirmed device
        if device.verify_token(token):
            device.confirmed = True
            device.save()
            messages.success(request, 'Two-Factor Authentication has been enabled successfully!')
        else:
            messages.error(request, 'The verification code was incorrect. Please try again.')
        
        return redirect('schools:profile_2fa')


class TwoFactorDisableView(LoginRequiredMixin, View):
    """
    Handles the disabling of 2FA for the current user.
    """
    login_url = reverse_lazy('schools:staff_login')

    def post(self, request, *args, **kwargs):
        # Handle the case where request.user might be a string representation
        from apps.schools.models import StaffUser
        try:
            if isinstance(request.user, str):
                # If it's a string, try to get user from session
                user_id = request.session.get('_auth_user_id')
                if user_id:
                    user = StaffUser.objects.get(pk=user_id)
                else:
                    messages.error(request, 'Session expired. Please log in again.')
                    return redirect('schools:staff_login')
            elif hasattr(request.user, 'pk') and request.user.pk:
                # User object is fine
                user = request.user
            else:
                # Try to get user from session as fallback
                user_id = request.session.get('_auth_user_id')
                if user_id:
                    user = StaffUser.objects.get(pk=user_id)
                else:
                    messages.error(request, 'Unable to identify user. Please log in again.')
                    return redirect('schools:staff_login')
        except (StaffUser.DoesNotExist, AttributeError, ValueError) as e:
            messages.error(request, f'Authentication error: {str(e)}. Please log in again.')
            return redirect('schools:staff_login')
        
        # Delete all OTP devices for the user
        deleted_count, _ = TOTPDevice.objects.filter(user=user).delete()

        if deleted_count > 0:
            messages.success(request, 'Two-Factor Authentication has been disabled.')
        else:
            messages.info(request, 'No 2FA devices were found to disable.')
        
        return redirect('schools:profile_2fa')
    
    
    












# # Two-Factor Authentication Views for Staff Users
# from django.shortcuts import render, redirect
# from django.contrib.auth.decorators import login_required
# from django.contrib import messages
# from django.urls import reverse
# from django.http import JsonResponse
# from django.views.decorators.http import require_POST
# from django_otp.plugins.otp_totp.models import TOTPDevice
# from django_otp.util import random_hex
# from django.conf import settings
# import qrcode
# import io
# import base64

# # Custom login_required decorator that uses staff login URL
# def staff_login_required(view_func):
#     """Custom login_required decorator that redirects to staff login page"""
#     from django.contrib.auth.decorators import user_passes_test
#     from django.urls import reverse_lazy

#     def check_staff_login(user):
#         return user.is_authenticated

#     return user_passes_test(
#         check_staff_login,
#         login_url=reverse_lazy('schools:staff_login')
#     )(view_func)


# @staff_login_required
# def setup_2fa(request):
#     """Setup 2FA for the current staff user"""
#     # Simple fix: Get the user from the session if request.user is problematic
#     from apps.schools.models import StaffUser
#     from django.contrib.auth import get_user

#     # Handle the case where request.user might be a string representation
#     try:
#         if isinstance(request.user, str):
#             # If it's a string, try to get user from session
#             user_id = request.session.get('_auth_user_id')
#             if user_id:
#                 user = StaffUser.objects.get(pk=user_id)
#             else:
#                 messages.error(request, 'Session expired. Please log in again.')
#                 return redirect('schools:staff_login')
#         elif hasattr(request.user, 'pk') and request.user.pk:
#             # User object is fine
#             user = request.user
#         else:
#             # Try to get user from session as fallback
#             user_id = request.session.get('_auth_user_id')
#             if user_id:
#                 user = StaffUser.objects.get(pk=user_id)
#             else:
#                 messages.error(request, 'Unable to identify user. Please log in again.')
#                 return redirect('schools:staff_login')
#     except (StaffUser.DoesNotExist, AttributeError, ValueError) as e:
#         messages.error(request, f'Authentication error: {str(e)}. Please log in again.')
#         return redirect('schools:staff_login')

#     if request.method == 'POST':
#         # Create or get existing TOTP device
#         device, created = TOTPDevice.objects.get_or_create(
#             user=user,
#             name='default',
#             defaults={'confirmed': False}
#         )
        
#         if not created and device.confirmed:
#             messages.warning(request, '2FA is already enabled for your account.')
#             return redirect('schools:profile_2fa')
        
#         # Generate QR code
#         qr_url = device.config_url
#         qr = qrcode.QRCode(version=1, box_size=10, border=5)
#         qr.add_data(qr_url)
#         qr.make(fit=True)
        
#         img = qr.make_image(fill_color="black", back_color="white")
#         buffer = io.BytesIO()
#         img.save(buffer, format='PNG')
#         buffer.seek(0)
#         qr_code_data = base64.b64encode(buffer.getvalue()).decode()
        
#         context = {
#             'device': device,
#             'qr_code_data': qr_code_data,
#             'secret_key': device.key,
#             'view_title': 'Setup Two-Factor Authentication'
#         }
#         return render(request, 'schools/2fa_setup.html', context)
    
#     # Check if user already has 2FA enabled
#     has_2fa = TOTPDevice.objects.filter(user=user, confirmed=True).exists()
    
#     context = {
#         'has_2fa': has_2fa,
#         'view_title': 'Two-Factor Authentication'
#     }
#     return render(request, 'schools/2fa_status.html', context)


# @staff_login_required
# @require_POST
# def confirm_2fa(request):
#     """Confirm 2FA setup with verification code"""
#     # Get the actual user object
#     from apps.schools.models import StaffUser
#     try:
#         if hasattr(request.user, 'pk') and request.user.pk:
#             user = request.user
#         else:
#             user_id = request.session.get('_auth_user_id')
#             user = StaffUser.objects.get(pk=user_id) if user_id else None
#             if not user:
#                 messages.error(request, 'Unable to identify user. Please log in again.')
#                 return redirect('schools:staff_login')
#     except (StaffUser.DoesNotExist, AttributeError, ValueError):
#         messages.error(request, 'Unable to identify user. Please log in again.')
#         return redirect('schools:staff_login')

#     token = request.POST.get('token', '').strip()

#     if not token:
#         messages.error(request, 'Please enter the verification code.')
#         return redirect('schools:setup_2fa')

#     # Get the unconfirmed device
#     try:
#         device = TOTPDevice.objects.get(user=user, name='default', confirmed=False)
#     except TOTPDevice.DoesNotExist:
#         messages.error(request, 'No 2FA setup found. Please start the setup process again.')
#         return redirect('schools:setup_2fa')
    
#     # Verify the token
#     if device.verify_token(token):
#         device.confirmed = True
#         device.save()
#         messages.success(request, '2FA has been successfully enabled for your account!')
#         return redirect('schools:profile_2fa')
#     else:
#         messages.error(request, 'Invalid verification code. Please try again.')
#         return redirect('schools:setup_2fa')


# @staff_login_required
# @require_POST
# def disable_2fa(request):
#     """Disable 2FA for the current user"""
#     # Get the actual user object
#     from apps.schools.models import StaffUser
#     try:
#         if hasattr(request.user, 'pk') and request.user.pk:
#             user = request.user
#         else:
#             user_id = request.session.get('_auth_user_id')
#             user = StaffUser.objects.get(pk=user_id) if user_id else None
#             if not user:
#                 messages.error(request, 'Unable to identify user. Please log in again.')
#                 return redirect('schools:staff_login')
#     except (StaffUser.DoesNotExist, AttributeError, ValueError):
#         messages.error(request, 'Unable to identify user. Please log in again.')
#         return redirect('schools:staff_login')

#     # Delete all TOTP devices for the user
#     deleted_count = TOTPDevice.objects.filter(user=user).delete()[0]
    
#     if deleted_count > 0:
#         messages.success(request, '2FA has been disabled for your account.')
#     else:
#         messages.info(request, 'No 2FA devices found to disable.')
    
#     return redirect('schools:profile_2fa')


# @staff_login_required
# def profile_2fa(request):
#     """Show 2FA status and management options"""
#     # Get the actual user object - handle the string issue
#     from apps.schools.models import StaffUser
#     try:
#         if isinstance(request.user, str):
#             # If it's a string, try to get user from session
#             user_id = request.session.get('_auth_user_id')
#             if user_id:
#                 user = StaffUser.objects.get(pk=user_id)
#             else:
#                 messages.error(request, 'Session expired. Please log in again.')
#                 return redirect('schools:staff_login')
#         elif hasattr(request.user, 'pk') and request.user.pk:
#             # User object is fine
#             user = request.user
#         else:
#             # Try to get user from session as fallback
#             user_id = request.session.get('_auth_user_id')
#             if user_id:
#                 user = StaffUser.objects.get(pk=user_id)
#             else:
#                 messages.error(request, 'Unable to identify user. Please log in again.')
#                 return redirect('schools:staff_login')
#     except (StaffUser.DoesNotExist, AttributeError, ValueError) as e:
#         messages.error(request, f'Authentication error: {str(e)}. Please log in again.')
#         return redirect('schools:staff_login')

#     devices = TOTPDevice.objects.filter(user=user, confirmed=True)
#     has_2fa = devices.exists()

#     context = {
#         'has_2fa': has_2fa,
#         'devices': devices,
#         'view_title': 'Two-Factor Authentication Settings'
#     }
#     return render(request, 'schools/2fa_profile.html', context)
