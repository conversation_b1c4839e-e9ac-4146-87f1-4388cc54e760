# Generated manually to fix missing columns in fees app
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fees', '0006_remove_studentconcession_unique_student_concession_and_more'),
    ]

    operations = [
        # Add missing 'type' column to ConcessionType if it doesn't exist
        migrations.RunSQL(
            sql=[
                """
                DO $$
                BEGIN
                    -- Add type column if it doesn't exist
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'fees_concessiontype' AND column_name = 'type'
                    ) THEN
                        ALTER TABLE fees_concessiontype ADD COLUMN type VARCHAR(20) DEFAULT 'PERCENTAGE' NOT NULL;
                    END IF;
                END $$;
                """
            ],
            reverse_sql=[
                """
                DO $$
                BEGIN
                    IF EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'fees_concessiontype' AND column_name = 'type'
                    ) THEN
                        ALTER TABLE fees_concessiontype DROP COLUMN type;
                    END IF;
                END $$;
                """
            ]
        ),
    ]
