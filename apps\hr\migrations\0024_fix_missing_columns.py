# Generated manually to fix missing columns in hr app
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0023_add_missing_payslip_fields'),
    ]

    operations = [
        # Add missing columns to hr tables
        migrations.RunSQL(
            sql=[
                """
                DO $$
                BEGIN
                    -- Add name column to TaxBracket if it doesn't exist
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'hr_taxbracket' AND column_name = 'name'
                    ) THEN
                        ALTER TABLE hr_taxbracket ADD COLUMN name VARCHAR(100) DEFAULT 'Tax Bracket' NOT NULL;
                    END IF;
                    
                    -- Fix SalaryComponent type column (rename component_type to type if needed)
                    IF EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'hr_salarycomponent' AND column_name = 'component_type'
                    ) AND NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'hr_salarycomponent' AND column_name = 'type'
                    ) THEN
                        ALTER TABLE hr_salarycomponent RENAME COLUMN component_type TO type;
                    END IF;
                    
                    -- Add type column to SalaryComponent if it doesn't exist at all
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'hr_salarycomponent' AND column_name = 'type'
                    ) AND NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'hr_salarycomponent' AND column_name = 'component_type'
                    ) THEN
                        ALTER TABLE hr_salarycomponent ADD COLUMN type VARCHAR(10) DEFAULT 'EARNING' NOT NULL;
                    END IF;
                END $$;
                """
            ],
            reverse_sql=[
                """
                DO $$
                BEGIN
                    IF EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'hr_taxbracket' AND column_name = 'name'
                    ) THEN
                        ALTER TABLE hr_taxbracket DROP COLUMN name;
                    END IF;
                    
                    IF EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'hr_salarycomponent' AND column_name = 'type'
                    ) THEN
                        ALTER TABLE hr_salarycomponent RENAME COLUMN type TO component_type;
                    END IF;
                END $$;
                """
            ]
        ),
    ]
