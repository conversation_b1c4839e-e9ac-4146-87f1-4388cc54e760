# Generated manually to ensure BudgetItem table exists
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('finance', '0002_initial'),
        ('accounting', '0004_fix_missing_columns'),
    ]

    operations = [
        # Ensure BudgetItem table exists with all required columns
        migrations.RunSQL(
            sql=[
                """
                DO $$
                BEGIN
                    -- Create BudgetItem table if it doesn't exist
                    IF NOT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = 'finance_budgetitem'
                    ) THEN
                        CREATE TABLE finance_budgetitem (
                            id BIGSERIAL PRIMARY KEY,
                            created_at TIMESTAMP WITH TIME ZONE,
                            updated_at TIMESTAMP WITH TIME ZONE,
                            name VARCHAR(150) UNIQUE NOT NULL,
                            description TEXT,
                            budget_item_type VARCHAR(10) NOT NULL,
                            linked_coa_account_id BIGINT REFERENCES accounting_account(id) ON DELETE RESTRICT
                        );
                        
                        -- <PERSON><PERSON> indexes
                        CREATE INDEX finance_budgetitem_linked_coa_account_id_idx ON finance_budgetitem(linked_coa_account_id);
                        CREATE INDEX finance_budgetitem_name_idx ON finance_budgetitem(name);
                    END IF;
                END $$;
                """
            ],
            reverse_sql=[
                """
                DROP TABLE IF EXISTS finance_budgetitem CASCADE;
                """
            ]
        ),
    ]
