# Generated by Django 5.1.7 on 2025-07-18 21:51

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('schools', '0004_fix_schoolprofile_constraints'),
    ]

    operations = [
        migrations.CreateModel(
            name='StaffTwoFactor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('secret_key', models.CharField(help_text='Base32 encoded secret key for TOTP generation', max_length=32)),
                ('is_enabled', models.<PERSON><PERSON>an<PERSON>ield(default=False, help_text='Whether 2FA is enabled for this user')),
                ('backup_codes', models.J<PERSON>NField(blank=True, default=list, help_text='List of backup codes for account recovery')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('staff_user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='two_factor', to='schools.staffuser', verbose_name='Staff User')),
            ],
            options={
                'verbose_name': 'Staff Two-Factor Authentication',
                'verbose_name_plural': 'Staff Two-Factor Authentication',
            },
        ),
    ]
