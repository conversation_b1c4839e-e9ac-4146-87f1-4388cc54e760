# Custom authentication backend for 2FA enforcement

from django.contrib.auth.backends import BaseBackend
from django.contrib.auth import get_user_model
from .models import <PERSON><PERSON><PERSON>, StaffTwoFactor
import time


class StaffTwoFactorBackend(BaseBackend):
    """
    Custom authentication backend that enforces 2FA for staff users.
    """
    
    def authenticate(self, request, username=None, password=None, totp_token=None, skip_password_check=False, **kwargs):
        """
        Authenticate user with email/password and optional TOTP token.
        """
        if not username:
            return None

        try:
            # Get user by email
            user = StaffUser.objects.get(email=username)
        except StaffUser.DoesNotExist:
            return None

        # Check password (unless skipping for 2FA step)
        if not skip_password_check:
            if not password or not user.check_password(password):
                return None
        
        # Check if user is active
        if not user.is_active:
            return None
        
        # Check if user has 2FA enabled
        try:
            two_factor = StaffTwoFactor.objects.get(staff_user=user)
            if two_factor.is_enabled:
                # 2FA is enabled, require TOTP token
                if not totp_token:
                    # Store partial authentication in session
                    if request:
                        request.session['partial_auth_user_id'] = user.id
                        request.session['partial_auth_timestamp'] = time.time()
                    return None  # Require 2FA token
                
                # Verify TOTP token
                if not two_factor.verify_token(totp_token):
                    return None  # Invalid token
                
                # Clear partial auth session
                if request:
                    request.session.pop('partial_auth_user_id', None)
                    request.session.pop('partial_auth_timestamp', None)
        
        except StaffTwoFactor.DoesNotExist:
            # No 2FA setup, allow login without token
            pass
        
        return user
    
    def get_user(self, user_id):
        """
        Get user by ID.
        """
        try:
            return StaffUser.objects.get(pk=user_id)
        except StaffUser.DoesNotExist:
            return None


def is_partial_auth_valid(request):
    """
    Check if partial authentication is valid (not expired).
    """
    if not request.session.get('partial_auth_user_id'):
        return False
    
    timestamp = request.session.get('partial_auth_timestamp', 0)
    # Partial auth expires after 5 minutes
    return (time.time() - timestamp) < 300


def get_partial_auth_user(request):
    """
    Get the partially authenticated user.
    """
    if not is_partial_auth_valid(request):
        return None
    
    user_id = request.session.get('partial_auth_user_id')
    if not user_id:
        return None
    
    try:
        return StaffUser.objects.get(pk=user_id)
    except StaffUser.DoesNotExist:
        return None


def clear_partial_auth(request):
    """
    Clear partial authentication from session.
    """
    request.session.pop('partial_auth_user_id', None)
    request.session.pop('partial_auth_timestamp', None)
