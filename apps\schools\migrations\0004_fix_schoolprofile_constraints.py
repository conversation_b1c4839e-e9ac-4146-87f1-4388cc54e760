# Generated manually to fix SchoolProfile constraints
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('schools', '0003_alter_schoolclass_options_schoolclass_order_and_more'),
    ]

    operations = [
        # Fix SchoolProfile table constraints
        migrations.RunSQL(
            sql=[
                """
                DO $$
                BEGIN
                    -- Remove NOT NULL constraint from name column if it exists
                    IF EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'schools_schoolprofile' 
                        AND column_name = 'name' 
                        AND is_nullable = 'NO'
                    ) THEN
                        ALTER TABLE schools_schoolprofile ALTER COLUMN name DROP NOT NULL;
                    END IF;
                    
                    -- Update any existing NULL name values to prevent constraint violations
                    UPDATE schools_schoolprofile 
                    SET name = COALESCE(school_name_override, 'School Profile') 
                    WHERE name IS NULL;
                    
                    -- Add any missing columns that should exist based on the model
                    -- Add school_name_override if it doesn't exist
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'schools_schoolprofile' AND column_name = 'school_name_override'
                    ) THEN
                        ALTER TABLE schools_schoolprofile ADD COLUMN school_name_override VARCHAR(255);
                    END IF;
                    
                    -- Add school_name_on_reports if it doesn't exist
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'schools_schoolprofile' AND column_name = 'school_name_on_reports'
                    ) THEN
                        ALTER TABLE schools_schoolprofile ADD COLUMN school_name_on_reports VARCHAR(255);
                    END IF;
                    
                    -- Add currency_symbol if it doesn't exist
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'schools_schoolprofile' AND column_name = 'currency_symbol'
                    ) THEN
                        ALTER TABLE schools_schoolprofile ADD COLUMN currency_symbol VARCHAR(5) DEFAULT '$';
                    END IF;
                    
                    -- Add default_due_days if it doesn't exist
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'schools_schoolprofile' AND column_name = 'default_due_days'
                    ) THEN
                        ALTER TABLE schools_schoolprofile ADD COLUMN default_due_days INTEGER DEFAULT 15;
                    END IF;
                    
                    -- Add timezone if it doesn't exist
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'schools_schoolprofile' AND column_name = 'timezone'
                    ) THEN
                        ALTER TABLE schools_schoolprofile ADD COLUMN timezone VARCHAR(50) DEFAULT 'UTC';
                    END IF;
                    
                    -- Add language if it doesn't exist
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'schools_schoolprofile' AND column_name = 'language'
                    ) THEN
                        ALTER TABLE schools_schoolprofile ADD COLUMN language VARCHAR(10) DEFAULT 'en';
                    END IF;
                END $$;
                """
            ],
            reverse_sql=[
                """
                -- This is a data fix migration, reverse is not practical
                SELECT 1;
                """
            ]
        ),
    ]
