{% extends "tenant_base.html" %}
{% load static i18n %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'schools:setup_2fa' %}">Two-Factor Authentication</a></li>
            <li class="breadcrumb-item active" aria-current="page">Setup</li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-shield-plus me-2"></i>Setup Two-Factor Authentication
                    </h4>
                </div>
                <div class="card-body">
                    {% include "partials/_messages.html" %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Step 1: Install an Authenticator App</h5>
                            <p>Download and install one of these authenticator apps on your mobile device:</p>
                            <ul>
                                <li><strong>Google Authenticator</strong> (iOS/Android)</li>
                                <li><strong>Microsoft Authenticator</strong> (iOS/Android)</li>
                                <li><strong>Authy</strong> (iOS/Android/Desktop)</li>
                                <li><strong>1Password</strong> (Premium)</li>
                            </ul>
                            
                            <h5 class="mt-4">Step 2: Scan QR Code</h5>
                            <p>Open your authenticator app and scan this QR code:</p>
                            
                            <div class="text-center mb-3">
                                <img src="data:image/png;base64,{{ qr_code_data }}" 
                                     alt="2FA QR Code" 
                                     class="img-fluid border rounded"
                                     style="max-width: 200px;">
                            </div>
                            
                            <div class="alert alert-info">
                                <small>
                                    <strong>Can't scan?</strong> Manually enter this key in your app:<br>
                                    <code>{{ secret_key }}</code>
                                </small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>Step 3: Enter Verification Code</h5>
                            <p>Enter the 6-digit code from your authenticator app to complete setup:</p>
                            
                            <form method="post" action="{% url 'schools:confirm_2fa' %}">
                                {% csrf_token %}
                                <div class="mb-3">
                                    <label for="token" class="form-label">Verification Code</label>
                                    <input type="text" 
                                           class="form-control form-control-lg text-center" 
                                           id="token" 
                                           name="token" 
                                           placeholder="000000"
                                           maxlength="6"
                                           pattern="[0-9]{6}"
                                           required
                                           style="font-size: 1.5rem; letter-spacing: 0.5rem;">
                                    <div class="form-text">Enter the 6-digit code from your authenticator app</div>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="bi bi-check-circle me-2"></i>Verify and Enable 2FA
                                    </button>
                                    <a href="{% url 'schools:setup_2fa' %}" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-left me-2"></i>Back
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-format the token input
document.getElementById('token').addEventListener('input', function(e) {
    // Remove any non-digit characters
    this.value = this.value.replace(/\D/g, '');
    
    // Limit to 6 digits
    if (this.value.length > 6) {
        this.value = this.value.slice(0, 6);
    }
});
</script>
{% endblock %}
