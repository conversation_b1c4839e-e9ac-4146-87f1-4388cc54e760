{% extends "base.html" %}

{% load i18n %}

{% block title %}{{ view_title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">{{ view_title }}</h1>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                
                {% if has_2fa %}
                <!-- === 2FA IS ENABLED === -->
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-shield-alt"></i> {% trans "Status: Enabled" %}
                    </h6>
                </div>
                <div class="card-body">
                    <p>
                        {% trans "Two-Factor Authentication is currently active on your account. When you sign in, you will be required to provide a code from your authenticator app." %}
                    </p>
                    <hr>
                    <p class="text-muted">{% trans "Device Name:" %} <strong>{{ device.name|default:'Default' }}</strong></p>
                    
                    <form action="{% url 'schools:disable_2fa' %}" method="post" onsubmit="return confirm('Are you sure you want to disable 2FA? This will reduce your account security.');">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-times-circle"></i> {% trans "Disable 2FA" %}
                        </button>
                    </form>
                </div>

                {% else %}
                <!-- === 2FA IS DISABLED (SETUP PROCESS) === -->
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-exclamation-triangle"></i> {% trans "Status: Disabled" %}
                    </h6>
                </div>
                <div class="card-body">
                    <p>
                        {% trans "Protect your account from unauthorized access by enabling Two-Factor Authentication. You will need an authenticator app like Google Authenticator, Microsoft Authenticator, or Authy." %}
                    </p>
                    <hr>
                    
                    <h5 class="mb-3">{% trans "Step 1: Scan this QR Code" %}</h5>
                    <div class="text-center mb-3">
                        <img src="data:image/png;base64,{{ qr_code_data }}" alt="QR Code for 2FA Setup">
                    </div>
                    <p class="text-center text-muted small">
                        {% trans "Can't scan the code? Enter this key manually:" %}<br>
                        <code class="fs-5">{{ secret_key }}</code>
                    </p>
                    <hr>

                    <h5 class="mb-3">{% trans "Step 2: Verify Your Device" %}</h5>
                    <p>{% trans "Enter the 6-digit code from your authenticator app to complete the setup." %}</p>
                    
                    <form action="{% url 'schools:profile_2fa' %}" method="post">
                        {% csrf_token %}
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <input type="text" name="token" class="form-control form-control-lg" placeholder="123456" required autocomplete="off" pattern="[0-9]{6}">
                            </div>
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-check-circle"></i> {% trans "Enable 2FA" %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}


