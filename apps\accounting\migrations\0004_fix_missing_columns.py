# Generated manually to fix missing columns in accounting app
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounting', '0003_alter_account_options_and_more'),
    ]

    operations = [
        # Add missing 'description' column to AccountType if it doesn't exist
        migrations.RunSQL(
            sql=[
                """
                DO $$
                BEGIN
                    -- Add description column if it doesn't exist
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'accounting_accounttype' AND column_name = 'description'
                    ) THEN
                        ALTER TABLE accounting_accounttype ADD COLUMN description TEXT;
                    END IF;
                END $$;
                """
            ],
            reverse_sql=[
                """
                DO $$
                BEGIN
                    IF EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'accounting_accounttype' AND column_name = 'description'
                    ) THEN
                        ALTER TABLE accounting_accounttype DROP COLUMN description;
                    END IF;
                END $$;
                """
            ]
        ),
    ]
