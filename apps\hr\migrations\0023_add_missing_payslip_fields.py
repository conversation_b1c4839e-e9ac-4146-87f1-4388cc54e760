# Generated manually to fix missing payslip fields
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0022_alter_leavebalance_unique_together'),
    ]

    operations = [
        # Add missing fields to payslip table if they don't exist
        migrations.RunSQL(
            sql=[
                """
                DO $$
                BEGIN
                    -- Add bonuses column if it doesn't exist
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'hr_payslip' AND column_name = 'bonuses'
                    ) THEN
                        ALTER TABLE hr_payslip ADD COLUMN bonuses NUMERIC(10,2) DEFAULT 0.00 NOT NULL;
                    END IF;
                    
                    -- Add loan_repayments column if it doesn't exist
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'hr_payslip' AND column_name = 'loan_repayments'
                    ) THEN
                        ALTER TABLE hr_payslip ADD COLUMN loan_repayments NUMERIC(10,2) DEFAULT 0.00 NOT NULL;
                    END IF;
                    
                    -- Add pension_deductions column if it doesn't exist
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'hr_payslip' AND column_name = 'pension_deductions'
                    ) THEN
                        ALTER TABLE hr_payslip ADD COLUMN pension_deductions NUMERIC(10,2) DEFAULT 0.00 NOT NULL;
                    END IF;
                    
                    -- Add notes column if it doesn't exist
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'hr_payslip' AND column_name = 'notes'
                    ) THEN
                        ALTER TABLE hr_payslip ADD COLUMN notes TEXT;
                    END IF;
                    
                    -- Rename gross_pay to gross_earnings if needed
                    IF EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'hr_payslip' AND column_name = 'gross_pay'
                    ) AND NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'hr_payslip' AND column_name = 'gross_earnings'
                    ) THEN
                        ALTER TABLE hr_payslip RENAME COLUMN gross_pay TO gross_earnings;
                    END IF;
                    
                    -- Rename net_pay to net_pay if needed (this should already be correct)
                    -- Just ensuring consistency
                    
                END $$;
                """
            ],
            reverse_sql=[
                # Reverse operations - remove the columns if they were added
                """
                DO $$
                BEGIN
                    IF EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'hr_payslip' AND column_name = 'bonuses'
                    ) THEN
                        ALTER TABLE hr_payslip DROP COLUMN bonuses;
                    END IF;
                    
                    IF EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'hr_payslip' AND column_name = 'loan_repayments'
                    ) THEN
                        ALTER TABLE hr_payslip DROP COLUMN loan_repayments;
                    END IF;
                    
                    IF EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'hr_payslip' AND column_name = 'pension_deductions'
                    ) THEN
                        ALTER TABLE hr_payslip DROP COLUMN pension_deductions;
                    END IF;
                    
                    IF EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'hr_payslip' AND column_name = 'notes'
                    ) THEN
                        ALTER TABLE hr_payslip DROP COLUMN notes;
                    END IF;
                END $$;
                """
            ]
        ),
    ]
