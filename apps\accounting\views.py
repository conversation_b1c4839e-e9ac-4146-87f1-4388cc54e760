# apps/fees/views.py
from django.views.generic import ListView
from django.urls import reverse_lazy
from apps.fees.models import FeeHead

from django.utils.translation import gettext_lazy as _

from django.shortcuts import render, redirect, get_object_or_404
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, TemplateView
from django.contrib.messages.views import SuccessMessageMixin
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin # For view protection
from django.contrib import messages # For manual messages if needed
from django.db import IntegrityError # For handling potential delete errors

from apps.common.mixins import TenantPermissionRequiredMixin

# Models
from apps.accounting.models import Account as Account, JournalEntry, JournalLine, AccountType

# Forms
from .forms import AccountForm, GeneralLedgerFilterForm # Ensure JournalEntryForm is defined if used later
from django.db.models import Su<PERSON>, Case, When, F, DecimalField
from decimal import Decimal

from .models import JournalEntry, JournalLine # Ensure these are defined in models.py
from .forms import JournalEntryForm, JournalEntryLineInlineFormSet # Import the new forms
from django.utils import timezone # For default date
from .forms import AccountForm, JournalEntryForm

from django.db.models import Prefetch # Import Prefetch

from apps.accounting.models import Account # Assuming ChartOfAccount is in the same app's models.py

from django.http import Http404 # For raising 404 if JE is not editable

from django.shortcuts import render, redirect, get_object_or_404
from django.views.generic import CreateView, ListView, DetailView, UpdateView, DeleteView # UpdateView for later
from django.urls import reverse_lazy, reverse
from django.contrib.messages.views import SuccessMessageMixin
from django.contrib import messages
from django.db import transaction # For atomic operations on save
from django.http import HttpResponseRedirect
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.decorators import login_required # For function-based view
from django.views.decorators.http import require_POST # For post_journal_entry_view

# Assuming your mixins for tenant permission are in common.mixins
# Adjust the import path if your mixin is elsewhere or named differently
from apps.common.mixins import TenantPermissionRequiredMixin # Or StaffPermissionRequiredMixin etc.

from .models import JournalEntry, JournalLine, Account # AccountType if needed for filters
from .forms import JournalEntryForm, JournalEntryLineFormSet

import logging
logger = logging.getLogger(__name__)



# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =

class FeeHeadListView(ListView): # Add LoginRequiredMixin later
    model = FeeHead
    template_name = 'fees/fee_head_list.html' # Check this path
    context_object_name = 'fee_heads'         # Check this variable name
    # paginate_by = 20 # Optional
    # login_url = reverse_lazy('schools:staff_login') # Add when LoginRequiredMixin is active

    def get_queryset(self):
        print(f"--- FeeHeadListView: get_queryset called for tenant: {self.request.tenant.schema_name if hasattr(self.request, 'tenant') else 'Unknown'} ---")
        # django-tenants automatically scopes .all() to the current tenant's schema
        qs = FeeHead.objects.select_related('income_account').all().order_by('name') # Select related for efficiency
        print(f"--- FeeHeadListView: Queryset count: {qs.count()} ---")
        return qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Manage Fee Heads"
        print(f"--- FeeHeadListView: Context prepared. Fee heads in context: {len(context.get('fee_heads', []))} ---")
        return context



class AccountListView(LoginRequiredMixin, ListView):
    login_url = reverse_lazy('schools:staff_login') # Redirect to staff login if not authenticated
    model = Account
    template_name = 'accounting/account_list.html'
    context_object_name = 'accounts' # Template expects 'accounts'
    paginate_by = 25  # Show 25 accounts per page

    # permission_required = 'accounting.view_account' # Correct permission for Account model

    def get_queryset(self):
        """
        Returns the full, ordered tree of accounts.
        The ordering is critical for django-mptt.
        """
        queryset = Account.objects.all() # MPTT manager handles default ordering

        # If you have a filter form, you can apply it here
        # self.filter = AccountFilter(self.request.GET, queryset=queryset)
        # return self.filter.qs
        
        # If no filter, just return the queryset. MPTT's default manager
        # should already apply the correct ordering by tree_id and lft.
        # To be explicit, you can add it:
        return queryset.order_by('tree_id', 'lft')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Chart of Accounts"
        return context
    
    

class AccountCreateView(LoginRequiredMixin, SuccessMessageMixin, CreateView):
    model = Account
    form_class = AccountForm
    template_name = 'accounting/account_form.html' # Ensure this template exists
    success_url = reverse_lazy('accounting:account_list')
    success_message = "Account '%(name)s' (%(code)s) created successfully."
    login_url = reverse_lazy('schools:staff_login')
    # permission_required = 'accounting.add_account'

    def get_initial(self):
        initial = super().get_initial()
        parent_id = self.request.GET.get('parent_id')
        if parent_id:
            try:
                initial['parent_account'] = Account.objects.get(pk=parent_id)
            except Account.DoesNotExist:
                pass # Silently ignore if parent_id is invalid, form will show all options
        return initial


    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # If AccountForm's __init__ needs tenant (e.g., for parent_account queryset filtering
        # beyond just the current schema, though usually not needed for this model)
        # kwargs['tenant'] = self.request.tenant
        return kwargs


    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = 'Add New Account to Chart'
        return context

    # form_valid can use default super behavior


class AccountUpdateView(LoginRequiredMixin, SuccessMessageMixin, UpdateView):
    model = Account
    form_class = AccountForm
    template_name = 'accounting/account_form.html'
    success_url = reverse_lazy('accounting:account_list')
    success_message = "Account '%(name)s' (%(code)s) updated successfully."
    context_object_name = 'account' # Optional: if template uses 'account'
    login_url = reverse_lazy('schools:staff_login')
    # permission_required = 'accounting.change_account'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # kwargs['tenant'] = self.request.tenant # If form needs it
        # Pass instance to form to ensure parent_account queryset excludes self
        # This is handled by AccountForm's __init__ already.
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f'Update Account: {self.object.name}'
        return context


class AccountDeleteView(LoginRequiredMixin, SuccessMessageMixin, DeleteView):
    model = Account
    template_name = 'accounting/account_confirm_delete.html' # Ensure this template exists
    success_url = reverse_lazy('accounting:account_list')
    # success_message defined by get_success_message_dict
    context_object_name = 'account' # Use 'account' for clarity in template
    login_url = reverse_lazy('schools:staff_login')
    # permission_required = 'accounting.delete_account'

    def get_success_message(self, cleaned_data): # For SuccessMessageMixin
        return f"Account '{self.object.name}' ({self.object.code}) deleted successfully."

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = 'Confirm Delete Account'
        context['delete_message'] = (
            f"Are you sure you want to delete the account: "
            f"<strong>{self.object.code} - {self.object.name}</strong>?<br>"
            f"If this account has child accounts or is used in journal entries, "
            f"deletion might be restricted or have consequences."
        )
        return context

    def form_valid(self, form):
        # Custom check before deletion (on_delete=PROTECT on JE.account would also prevent this)
        if self.object.journal_lines.exists() or self.object.child_accounts.exists():
            messages.error(
                self.request,
                f"Cannot delete account '{self.object.name}' as it has associated "
                f"journal entries or child accounts. Reassign them or delete them first."
            )
            # Redirect back to list or detail page
            return redirect(reverse('accounting:account_list')) # Or self.object.get_absolute_url()
        
        # If no dependencies, proceed with deletion
        success_message = self.get_success_message({}) # Pass empty dict for mixin
        response = super().form_valid(form)
        messages.success(self.request, success_message) # Send message after successful delete
        return response


# # Journal Entry Views

# D:\school_fees_saas_v2\apps\accounting\views.py

from django.shortcuts import render, redirect, get_object_or_404
from django.views.generic import CreateView, ListView, DetailView # UpdateView for later
from django.urls import reverse_lazy, reverse
from django.contrib import messages # Removed SuccessMessageMixin, handling manually
from django.db import transaction
from django.http import HttpResponseRedirect, Http404 # Added Http404
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_POST

from apps.common.mixins import TenantPermissionRequiredMixin # Using this for permissions

from .models import JournalEntry, JournalLine, Account # Make sure all are imported
from .forms import JournalEntryForm, JournalEntryLineFormSet # Using this name

import logging
logger = logging.getLogger(__name__)

# --- Journal Entry Views ---

class JournalEntryListView(TenantPermissionRequiredMixin, ListView):
    model = JournalEntry
    template_name = 'accounting/journalentry_list.html' # Your template name
    context_object_name = 'journal_entries'
    permission_required = 'accounting.view_journalentry' # Define appropriate permission
    paginate_by = 20 # Or your preferred 50

    def get_queryset(self):
        # Assumes JournalEntry is tenant-scoped by schema or explicit FK (if shared model)
        # If explicit FK: queryset = JournalEntry.objects.filter(tenant=self.request.tenant)
        return JournalEntry.objects.all().select_related('created_by').order_by('-date', '-pk')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("General Journal Entries") # Changed from "Manual" to be more general
        return context


class JournalEntryCreateView(TenantPermissionRequiredMixin, CreateView):
    model = JournalEntry
    form_class = JournalEntryForm
    template_name = 'accounting/journalentry_form.html'
    permission_required = 'accounting.add_journalentry'

    def get_success_url(self):
        # Success message is now added here
        messages.success(self.request, _(f"Draft Journal Entry JE-{self.object.pk:05d} created successfully. Please review and post."))
        return reverse('accounting:journalentry_detail', kwargs={'pk': self.object.pk})

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        # If JournalEntry model has a direct tenant FK:
        # kwargs['tenant'] = self.request.tenant
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Create Manual Journal Entry")
        # Pass tenant to formset kwargs if BaseJournalEntryLineFormSet.__init__ needs it
        # to filter ChartOfAccount queryset.
        formset_kwargs = {
            'prefix': 'lines',
            # 'form_kwargs': {'tenant': self.request.tenant} # Example
        }
        if self.request.POST:
            context['line_formset'] = JournalEntryLineFormSet(self.request.POST, **formset_kwargs)
        else:
            context['line_formset'] = JournalEntryLineFormSet(**formset_kwargs)
        context['is_create_view'] = True
        return context

    def form_valid(self, form):
        context = self.get_context_data(form=form) # Pass form to get_context_data
        line_formset = context['line_formset']

        if not line_formset.is_valid():
            logger.warning(f"JournalEntryCreateView: Line formset is invalid. Errors: {line_formset.errors} Non-form: {line_formset.non_form_errors()}")
            # Add formset errors to messages for display by the template
            for error_list in line_formset.errors: 
                if error_list: # Check if the error_list for a form is not empty
                    for field, errors in error_list.items():
                        for error in errors:
                            messages.error(self.request, _("Line Item Error (%(field)s): %(error)s") % {'field': field, 'error': error})
            if line_formset.non_form_errors():
                for error in line_formset.non_form_errors():
                    messages.error(self.request, error) # Error from formset's clean
            return self.form_invalid(form) # This re-renders the form with both form & line_formset errors

        try:
            with transaction.atomic():
                # The JournalEntryForm's save method handles created_by, entry_type, status
                self.object = form.save() 

                line_formset.instance = self.object
                line_formset.save()
                logger.info(f"JournalEntryCreateView: Draft Journal Entry JE-{self.object.pk} and lines saved by user {self.request.user.email}.")
        except Exception as e:
            logger.error(f"JournalEntryCreateView: Error during atomic save for JE by {self.request.user.email}: {e}", exc_info=True)
            messages.error(self.request, _("An error occurred while saving the journal entry. Please review and try again."))
            return self.form_invalid(form)

        return HttpResponseRedirect(self.get_success_url()) # Success message handled in get_success_url

    def form_invalid(self, form):
        logger.warning(f"JournalEntryCreateView: Main form invalid. Errors: {form.errors}")
        # Add main form errors to messages
        for field, errors in form.errors.items():
            for error in errors:
                if field == '__all__':
                    messages.error(self.request, error)
                else:
                    messages.error(self.request, _("Entry Detail Error (%(field)s): %(error)s") % {'field': form[field].label, 'error': error})
        
        # The line_formset is already in context if it's a POST request due to get_context_data.
        # If it's somehow not (e.g., direct call to form_invalid not via dispatch),
        # ensure it's re-added for the template.
        # context = self.get_context_data(form=form) # This will re-initialize line_formset
        # if 'line_formset' not in context or not context['line_formset'].is_bound:
            # context['line_formset'] = JournalEntryLineFormSet(self.request.POST or None, prefix='lines', form_kwargs=...)

        return super().form_invalid(form) # This calls render_to_response with the form and updated context


class JournalEntryDetailView(TenantPermissionRequiredMixin, DetailView):
    model = JournalEntry
    template_name = 'accounting/journalentry_detail.html' # Your template
    context_object_name = 'journal_entry' # Changed from 'entry' for clarity
    permission_required = 'accounting.view_journalentry'

    def get_queryset(self):
        # Prefetch lines and related accounts for efficiency
        return super().get_queryset().prefetch_related('lines__account__account_type')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        je = self.get_object() # je is now self.object
        context['view_title'] = _(f"Journal Entry Details: JE-{je.pk:05d}")
        
        # Totals and balance are now properties on the JournalEntry model
        # context['total_debit'] = je.total_debits
        # context['total_credit'] = je.total_credits
        # context['is_balanced'] = je.is_balanced
        # These are directly accessible via {{ journal_entry.total_debits }}, etc. in template

        can_post_perm = self.request.user.has_perm('accounting.change_journalentry') # Or specific 'post_journalentry'
        context['can_post'] = je.status == JournalEntry.EntryStatus.DRAFT and je.is_balanced and can_post_perm
        
        context['can_edit_draft'] = (
            je.status == JournalEntry.EntryStatus.DRAFT and 
            je.entry_type == JournalEntry.EntryType.MANUAL and # Only manual drafts
            self.request.user.has_perm('accounting.change_journalentry')
        )
        
        context['can_delete_draft'] = (
            je.status == JournalEntry.EntryStatus.DRAFT and
            je.entry_type == JournalEntry.EntryType.MANUAL and
            self.request.user.has_perm('accounting.delete_journalentry') # Check delete permission
        )# context['can_delete_draft'] = ... (similar logic for delete perm)
        return context


# Renamed login_url for consistency if using TenantPermissionRequiredMixin which might have its own defaults
# Staff login is usually handled by the mixin's get_login_url()
# @login_required(login_url=reverse_lazy('schools:staff_login'))
@require_POST
# Ideally, use a permission decorator if you have one for function views,
# or check permission inside.
def post_journal_entry_view(request, pk):
    # Permission check (if not handled by a decorator)
    if not request.user.has_perm('accounting.change_journalentry'): # Or a specific 'post_journalentry' permission
        messages.error(request, _("You do not have permission to post journal entries."))
        # Attempt to redirect to detail, otherwise to list
        try:
            detail_url = reverse('accounting:journalentry_detail', kwargs={'pk': pk})
            # Check if JE exists before trying to redirect to its detail
            get_object_or_404(JournalEntry, pk=pk) 
            return redirect(detail_url)
        except Http404:
            return redirect(reverse('accounting:journalentry_list'))

    journal_entry = get_object_or_404(JournalEntry, pk=pk)
    # Define detail_url after confirming journal_entry exists
    detail_url = reverse('accounting:journalentry_detail', kwargs={'pk': journal_entry.pk})


    if journal_entry.entry_type != JournalEntry.EntryType.MANUAL:
        messages.error(request, _("Only DRAFT manual journal entries can be posted through this action."))
        return redirect(detail_url)
    # The post_entry method in the model will check for DRAFT status and balance
    try:
        journal_entry.post_entry() # This method also saves
        messages.success(request, _(f"Journal Entry JE-{journal_entry.pk:05d} has been successfully posted."))
        logger.info(f"Journal Entry JE-{journal_entry.pk} POSTED by user {request.user.email}.")
    except ValueError as ve: # Raised by post_entry for status or balance issues
        logger.warning(f"Failed to post JE-{journal_entry.pk} by {request.user.email}: {ve}")
        messages.error(request, str(ve)) # Display the specific error from post_entry
    except Exception as e:
        logger.error(f"Error posting journal entry JE-{pk} by {request.user.email}: {e}", exc_info=True)
        messages.error(request, _("An unexpected error occurred while trying to post the journal entry."))
    
    return redirect(detail_url)



class JournalEntryUpdateView(TenantPermissionRequiredMixin, UpdateView):
    model = JournalEntry
    form_class = JournalEntryForm
    template_name = 'accounting/journalentry_form.html' # Reuse the create form template
    permission_required = 'accounting.change_journalentry' # Users need change permission to update
    context_object_name = 'journal_entry' # To match what detail view might use, or just 'object'

    def get_queryset(self):
        # Only allow updating DRAFT, MANUAL entries
        # Also, ensure it's for the current tenant if JE has a tenant FK (or rely on schema)
        return super().get_queryset().filter(
            status=JournalEntry.EntryStatus.DRAFT,
            entry_type=JournalEntry.EntryType.MANUAL
            # tenant=self.request.tenant # If JE model has direct tenant FK
        )

    def get_success_url(self):
        messages.success(self.request, _(f"Draft Journal Entry JE-{self.object.pk:05d} updated successfully."))
        return reverse('accounting:journalentry_detail', kwargs={'pk': self.object.pk})

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user # For last_modified_by
        # If JournalEntry model has a direct tenant FK and form needs it:
        # kwargs['tenant'] = self.request.tenant
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _(f"Update Draft Journal Entry: JE-{self.object.pk:05d}")
        
        formset_kwargs = {
            'prefix': 'lines',
            'instance': self.object, # Pass the JE instance to the formset
            # 'form_kwargs': {'tenant': self.request.tenant} # If COA filtering needed
        }
        if self.request.POST:
            context['line_formset'] = JournalEntryLineFormSet(self.request.POST, **formset_kwargs)
        else:
            context['line_formset'] = JournalEntryLineFormSet(**formset_kwargs)
        
        context['is_create_view'] = False # Flag for template if it behaves differently for update
        return context

    def form_valid(self, form):
        context = self.get_context_data(form=form)
        line_formset = context['line_formset']

        if not line_formset.is_valid():
            logger.warning(f"JournalEntryUpdateView: Line formset invalid for JE-{self.object.pk}. Errors: {line_formset.errors} Non-form: {line_formset.non_form_errors()}")
            for error_list in line_formset.errors:
                if error_list:
                    for field, errors in error_list.items():
                        for error in errors:
                            messages.error(self.request, _("Line Item Error (%(field)s): %(error)s") % {'field': field, 'error': error})
            if line_formset.non_form_errors():
                for error in line_formset.non_form_errors():
                    messages.error(self.request, error)
            return self.form_invalid(form)

        try:
            with transaction.atomic():
                # The JournalEntryForm's save method handles last_modified_by
                # Status and entry_type should not change for a draft update here
                self.object = form.save() 

                line_formset.instance = self.object
                line_formset.save() # Saves changes, adds new lines, deletes marked lines
                logger.info(f"JournalEntryUpdateView: Draft JE-{self.object.pk} updated by user {self.request.user.email}.")
        except Exception as e:
            logger.error(f"JournalEntryUpdateView: Error during atomic save for JE-{self.object.pk} by {self.request.user.email}: {e}", exc_info=True)
            messages.error(self.request, _("An error occurred while updating the journal entry. Please review and try again."))
            return self.form_invalid(form)
            
        return HttpResponseRedirect(self.get_success_url())

    def form_invalid(self, form):
        logger.warning(f"JournalEntryUpdateView: Main form invalid for JE-{self.object.pk if self.object else 'New'}. Errors: {form.errors}")
        # Similar error message handling as in CreateView
        for field, errors in form.errors.items():
            for error in errors:
                if field == '__all__':
                    messages.error(self.request, error)
                else:
                    messages.error(self.request, _("Entry Detail Error (%(field)s): %(error)s") % {'field': form[field].label, 'error': error})
        return super().form_invalid(form)

    def dispatch(self, request, *args, **kwargs):
        # Additional check to ensure object exists and meets criteria before dispatching
        # UpdateView's get_object will raise Http404 if not found by get_queryset
        # but we can add an explicit message if it's found but not a DRAFT/MANUAL.
        try:
            obj = self.get_object() # This uses get_queryset
            if obj.status != JournalEntry.EntryStatus.DRAFT or obj.entry_type != JournalEntry.EntryType.MANUAL:
                messages.error(request, _("Only DRAFT manual journal entries can be edited."))
                # Try to redirect to detail if possible, else list
                try:
                    return redirect(reverse('accounting:journalentry_detail', kwargs={'pk': obj.pk}))
                except:
                    return redirect(reverse_lazy('accounting:journalentry_list'))
        except Http404: # Object not found by get_queryset (e.g. not DRAFT/MANUAL or doesn't exist)
            messages.error(request, _("The requested draft journal entry was not found or cannot be edited."))
            return redirect(reverse_lazy('accounting:journalentry_list'))
        
        return super().dispatch(request, *args, **kwargs)



class JournalEntryDeleteView(TenantPermissionRequiredMixin, DeleteView):
    model = JournalEntry
    template_name = 'accounting/journalentry_confirm_delete.html' # Standard confirmation template
    permission_required = 'accounting.delete_journalentry' # Needs delete permission
    context_object_name = 'journal_entry' # Or 'object' by default
    success_url = reverse_lazy('accounting:journalentry_list')

    def get_queryset(self):
        # Only allow deleting DRAFT, MANUAL entries
        return super().get_queryset().filter(
            status=JournalEntry.EntryStatus.DRAFT,
            entry_type=JournalEntry.EntryType.MANUAL
            # tenant=self.request.tenant # If JE model has direct tenant FK
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _(f"Confirm Delete Draft JE-{self.object.pk:05d}")
        return context

    def form_valid(self, form):
        # This method is called when the POST request (confirmation) is valid.
        # DeleteView's default form_valid handles the deletion.
        object_pk = self.object.pk # Get PK before deletion for message
        object_desc = str(self.object) # Get description before deletion
        
        response = super().form_valid(form) # Performs the deletion
        
        messages.success(self.request, _(f"Draft Journal Entry JE-{object_pk:05d} ('{object_desc}') has been successfully deleted."))
        logger.info(f"JournalEntryDeleteView: Draft JE-{object_pk} deleted by user {self.request.user.email}.")
        return response # Returns HttpResponseRedirect to success_url

    def dispatch(self, request, *args, **kwargs):
        # Optional: Add extra check similar to UpdateView to provide a better message
        # if the object exists but doesn't meet deletion criteria (e.g., it's POSTED).
        # DeleteView's get_object (called by dispatch) uses get_queryset, so it will
        # raise Http404 if the object doesn't meet the DRAFT/MANUAL criteria.
        try:
            obj = self.get_object() # This will raise Http404 if not found by get_queryset
            # We don't need to check status/type again here as get_queryset handles it.
        except Http404:
            messages.error(request, _("The requested draft journal entry was not found or cannot be deleted (it may have been posted or is not a manual entry)."))
            return redirect(reverse_lazy('accounting:journalentry_list'))
        
        return super().dispatch(request, *args, **kwargs)



class AccountCreateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, CreateView):
    model = Account
    form_class = AccountForm
    template_name = 'accounting/account_form.html'
    permission_required = 'accounting.add_account'
    success_url = reverse_lazy('accounting:account_list')
    success_message = _("Account \"%(name)s\" (Code: %(code)s) was created successfully.") # Uses model fields

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = _('Create New Account')
        context['view_title'] = _('Create New Account Entry')
        return context

    def form_valid(self, form):
        logger.info(f"AccountCreateView: Creating account '{form.cleaned_data.get('name')}' by user '{self.request.user.email}'.")
        # self.object is set by super().form_valid()
        # SuccessMessageMixin will display self.success_message after redirect.
        return super().form_valid(form)

    def get_form_kwargs(self):
        """Pass request to the form if your AccountForm needs it."""
        kwargs = super().get_form_kwargs()
        # kwargs['request'] = self.request # Uncomment if AccountForm uses self.request
        return kwargs
    

class AccountUpdateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = Account
    form_class = AccountForm
    template_name = 'accounting/account_form.html'
    permission_required = 'accounting.change_account'
    context_object_name = 'account' # Or 'object' (default)
    success_url = reverse_lazy('accounting:account_list')
    success_message = _("Account \"%(name)s\" (Code: %(code)s) was updated successfully.")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = _('Edit Account')
        # self.object is the Account instance being edited
        context['view_title'] = _('Edit Account: {}').format(self.object.name if self.object else "")
        return context

    def form_valid(self, form):
        logger.info(f"AccountUpdateView: Updating account '{self.object.name}' (PK: {self.object.pk}) by user '{self.request.user.email}'.")
        return super().form_valid(form)

    def get_form_kwargs(self):
        """Pass request to the form if your AccountForm needs it."""
        kwargs = super().get_form_kwargs()
        # kwargs['request'] = self.request # Uncomment if AccountForm uses self.request
        return kwargs
    

class AccountDeleteView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, DeleteView):
    model = Account
    template_name = 'accounting/account_confirm_delete.html' 
    permission_required = 'accounting.delete_account'
    success_url = reverse_lazy('accounting:account_list')
    context_object_name = 'account' # Or 'object'
    # Success message is tricky with DeleteView if you want to use object attributes,
    # as the object is deleted before the message is usually processed.
    # A custom message can be added in post() or delete().
    # For now, SuccessMessageMixin might not show a dynamic message easily.

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = _('Confirm Delete Account')
        context['view_title'] = _('Delete Account: {}?').format(self.object.name if self.object else "")
        return context

    def post(self, request, *args, **kwargs):
        # Custom post to add message before deletion, as SuccessMessageMixin might not work well with DeleteView
        object_to_delete = self.get_object()
        object_name = str(object_to_delete) # Get name before it's deleted
        
        # MPTT specific: Check if it has children. May want to prevent deletion or warn.
        if hasattr(object_to_delete, 'get_children') and object_to_delete.get_children().exists():
            messages.error(request, _(
                "Cannot delete account \"%(name)s\" because it has sub-accounts. "
                "Please delete or re-parent its sub-accounts first.") % {'name': object_name}
            )
            return redirect(self.success_url) # Or back to the object's change page or list

        # If on_delete=models.PROTECT is used on related models pointing to Account,
        # this delete() will fail if there are related objects.
        try:
            response = super().post(request, *args, **kwargs) # This calls object.delete()
            messages.success(request, _("Account \"%(name)s\" was deleted successfully.") % {'name': object_name})
            return response
        except models.ProtectedError as e:
            logger.warning(f"AccountDeleteView: ProtectedError deleting account '{object_name}': {e}")
            messages.error(request, _(
                "Cannot delete account \"%(name)s\" because it is referenced by other records "
                "(e.g., journal entries, fee heads, default settings). "
                "Consider deactivating it instead.") % {'name': object_name}
            )
            return redirect(self.success_url) # Or redirect to a more appropriate error handling page

 
    


# D:\school_fees_saas_v2\apps\accounting\views.py

from django.shortcuts import render, redirect, get_object_or_404
# Ensure all necessary generic views are imported
from django.views.generic import (
    CreateView, 
    ListView, 
    DetailView, 
    UpdateView, 
    TemplateView  # <<< ADD THIS IMPORT
)
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.db import transaction
from django.http import HttpResponseRedirect, Http404
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_POST
from datetime import timedelta # For AccountLedgerView
from decimal import Decimal # For AccountLedgerView
from django.db.models import Sum, F, Q, DecimalField # Added Q for AccountLedgerView
from django.db.models.functions import Coalesce # For AccountLedgerView


from apps.common.mixins import TenantPermissionRequiredMixin

# Import your models and forms
from .models import JournalEntry, JournalLine, Account, AccountType # Added AccountType
from .forms import (
    JournalEntryForm, 
    JournalEntryLineFormSet, 
    AccountLedgerFilterForm # Ensure this form is defined
)

import logging
logger = logging.getLogger(__name__)

class AccountLedgerView(TenantPermissionRequiredMixin, TemplateView):
    template_name = 'accounting/account_ledger.html'
    permission_required = 'accounting.view_account' # Or a more specific "view_ledger" permission

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Account Ledger")
        
        request = self.request
        form = AccountLedgerFilterForm(request.GET or None) # Initialize with GET data or unbound
        context['filter_form'] = form

        selected_account = None
        transactions = []
        opening_balance = Decimal('0.00')
        closing_balance = Decimal('0.00')
        total_debits_period = Decimal('0.00')
        total_credits_period = Decimal('0.00')

        if form.is_valid():
            selected_account = form.cleaned_data.get('account')
            start_date = form.cleaned_data.get('start_date')
            end_date = form.cleaned_data.get('end_date') # This is required by the form

            if selected_account:
                context['selected_account_name'] = selected_account.name
                context['selected_account_code'] = selected_account.code
                context['start_date_filter'] = start_date
                context['end_date_filter'] = end_date

                # 1. Calculate Opening Balance (as of the day *before* start_date)
                # If no start_date, opening balance is effectively from the beginning of time for that account.
                opening_balance_date_cutoff = None
                if start_date:
                    opening_balance_date_cutoff = start_date - timedelta(days=1)
                
                qs_opening = JournalLine.objects.filter(account=selected_account)
                if opening_balance_date_cutoff:
                    qs_opening = qs_opening.filter(journal_entry__date__lte=opening_balance_date_cutoff)
                
                opening_agg = qs_opening.aggregate(
                    total_debit=Coalesce(Sum('debit_amount'), Decimal('0.00')),
                    total_credit=Coalesce(Sum('credit_amount'), Decimal('0.00'))
                )
                # Determine if account is debit or credit normal balance for correct OB calculation
                if selected_account.account_type.normal_balance == AccountType.NormalBalanceChoices.DEBIT:
                    opening_balance = opening_agg['total_debit'] - opening_agg['total_credit']
                else: # CREDIT normal balance
                    opening_balance = opening_agg['total_credit'] - opening_agg['total_debit']
                
                context['opening_balance'] = opening_balance
                running_balance = opening_balance

                # 2. Get Transactions for the Period
                qs_period = JournalLine.objects.filter(
                    account=selected_account,
                    journal_entry__status='POSTED' # Only POSTED entries
                ).select_related('journal_entry').order_by('journal_entry__date', 'journal_entry__pk', 'pk') # Order by date, then JE pk, then line pk

                if start_date:
                    qs_period = qs_period.filter(journal_entry__date__gte=start_date)
                if end_date: # Form makes end_date required
                    qs_period = qs_period.filter(journal_entry__date__lte=end_date)

                for line in qs_period:
                    transaction_amount = line.debit_amount - line.credit_amount # Net effect on a debit-normal account
                    
                    # Adjust running balance based on account's normal balance type
                    if selected_account.account_type.normal_balance == AccountType.NormalBalanceChoices.DEBIT:
                        running_balance += transaction_amount 
                    else: # CREDIT normal balance
                        running_balance -= transaction_amount

                    transactions.append({
                        'date': line.journal_entry.date,
                        'je_id': line.journal_entry.pk,
                        'description': line.journal_entry.description, # Or line.description if more specific
                        'reference': line.journal_entry.reference_number,
                        'debit': line.debit_amount,
                        'credit': line.credit_amount,
                        'balance': running_balance
                    })
                    total_debits_period += line.debit_amount
                    total_credits_period += line.credit_amount
                
                closing_balance = running_balance # The last running balance is the closing balance
        else:
            if request.GET: # If form was submitted but invalid
                logger.warning(f"AccountLedgerView: Filter form invalid. Errors: {form.errors.as_json()}")
                # errors are available in {{ filter_form.errors }} in template

        context['transactions'] = transactions
        context['closing_balance'] = closing_balance
        context['total_debits_period'] = total_debits_period
        context['total_credits_period'] = total_credits_period
        
        return context
    
    
from django import forms
from django.views.generic.base import View # For a simple confirmation GET
from django.utils import timezone # For default reversal date

# Simple form for reversal confirmation, could be more elaborate
class ReverseJournalEntryForm(forms.Form):
    reversal_date = forms.DateField(
        label=_("Reversal Date"),
        initial=timezone.now().date(),
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'})
    )
    reversal_description = forms.CharField(
        label=_("Reversal Description (Optional)"),
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
        help_text=_("If blank, default description will be used.")
    )


class ConfirmReverseJournalEntryView(TenantPermissionRequiredMixin, View): # Using View for GET
    permission_required = 'accounting.add_journalentry' # Reversing creates a new JE
    template_name = 'accounting/journalentry_confirm_reverse.html'

    def get(self, request, *args, **kwargs):
        original_je_pk = kwargs.get('pk')
        original_je = get_object_or_404(JournalEntry, pk=original_je_pk)

        if not original_je.can_be_reversed():
            messages.error(request, _(f"Journal Entry JE-{original_je.pk:05d} cannot be reversed (it might not be POSTED or already reversed)."))
            return redirect(reverse('accounting:journalentry_detail', kwargs={'pk': original_je.pk}))

        form = ReverseJournalEntryForm(initial={
            'reversal_date': timezone.now().date(), # Default to today
            'reversal_description': _(f"Reversal of JE-{original_je.pk:05d}: {original_je.description}")[:250]
        })
        
        context = {
            'view_title': _(f"Confirm Reverse Journal Entry JE-{original_je.pk:05d}"),
            'original_journal_entry': original_je,
            'form': form
        }
        return render(request, self.template_name, context)


@login_required # Or your staff permission decorator
@require_POST # This action should only be via POST
# @tenant_permission_required('accounting.add_journalentry') # If you have such a decorator
def reverse_journal_entry_action(request, pk):
    # Permission check if not handled by decorator
    if not request.user.has_perm('accounting.add_journalentry') or \
        not request.user.has_perm('accounting.change_journalentry'): # Might need change perm too
        messages.error(request, _("You do not have permission to reverse journal entries."))
        return redirect(reverse('accounting:journalentry_detail', kwargs={'pk': pk}))

    original_je = get_object_or_404(JournalEntry, pk=pk)
    original_je_detail_url = reverse('accounting:journalentry_detail', kwargs={'pk': original_je.pk})

    if not original_je.can_be_reversed():
        messages.error(request, _(f"Journal Entry JE-{original_je.pk:05d} cannot be reversed (status: {original_je.get_status_display()}, already reversed: {hasattr(original_je, 'reversed_by_entry')})."))
        return redirect(original_je_detail_url)

    form = ReverseJournalEntryForm(request.POST)
    if not form.is_valid():
        messages.error(request, _("Invalid reversal date or description. Please try again."))
        # To re-render confirmation page with errors, you'd ideally use ConfirmReverseJournalEntryView.post
        # For simplicity here, just redirecting back to detail.
        # Or store original_je in session to re-render confirm page with form errors.
        return redirect(original_je_detail_url)

    reversal_date = form.cleaned_data['reversal_date']
    reversal_description_override = form.cleaned_data.get('reversal_description')

    try:
        with transaction.atomic():
            # Create the new reversing JE
            reversing_je = JournalEntry.objects.create(
                date=reversal_date,
                description=reversal_description_override or _(f"Reversal of JE-{original_je.pk:05d}: {original_je.description}")[:250],
                reference_number=original_je.reference_number, # Or add "REV-" prefix
                entry_type=original_je.entry_type, # Keep same type, or make specific "REVERSAL" type
                status=JournalEntry.EntryStatus.POSTED, # Reversals are typically posted immediately
                created_by=request.user,
                # tenant=original_je.tenant, # If JE has direct tenant FK
                is_reversing_entry=True,
                reverses_entry=original_je
            )
            logger.info(f"Created reversing JE-{reversing_je.pk} for original JE-{original_je.pk} by {request.user.email}")

            # Create opposite lines
            for line in original_je.lines.all():
                JournalLine.objects.create(
                    journal_entry=reversing_je,
                    account=line.account,
                    debit_amount=line.credit_amount,  # Swap debit and credit
                    credit_amount=line.debit_amount, # Swap debit and credit
                    description=line.description or _(f"Reversal of line for {line.account.name}")
                )
            
            # Mark original entry's status as 'REVERSED' if you have such a status
            # Or rely on the reversed_by_entry link.
            # original_je.status = JournalEntry.EntryStatus.REVERSED 
            # original_je.save(update_fields=['status']) 
            # Note: The OneToOneField 'reversed_by_entry' on the original_je (from reverses_entry on reversing_je)
            # will be automatically set when reversing_je is saved IF the related_name is correct.
            # We set 'reverses_entry' on the new JE. The reverse relation 'reversed_by_entry' links back.

            messages.success(request, _(f"Journal Entry JE-{original_je.pk:05d} has been reversed by new Journal Entry JE-{reversing_je.pk:05d}."))
            logger.info(f"JE-{original_je.pk} reversed with JE-{reversing_je.pk} by {request.user.email}.")
            return redirect(reverse('accounting:journalentry_detail', kwargs={'pk': reversing_je.pk}))

    except Exception as e:
        logger.error(f"Error reversing journal entry JE-{original_je.pk} by {request.user.email}: {e}", exc_info=True)
        messages.error(request, _("An unexpected error occurred while trying to reverse the journal entry."))
        return redirect(original_je_detail_url)


class GeneralLedgerView(TenantPermissionRequiredMixin, TemplateView):
    """
    Comprehensive General Ledger view showing all transactions across all accounts.
    Provides filtering by account type, specific accounts, date range, and entry status.
    """
    template_name = 'accounting/general_ledger.html'
    permission_required = 'accounting.view_journalentry'  # Same permission as journal entries

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Initialize the filter form
        filter_form = GeneralLedgerFilterForm(self.request.GET or None)
        context['filter_form'] = filter_form

        # Get school profile for currency symbol
        try:
            from apps.schools.models import SchoolProfile
            school_profile = SchoolProfile.objects.first()
            context['school_profile'] = school_profile
        except ImportError:
            context['school_profile'] = None

        # Initialize empty context variables
        context['transactions'] = []
        context['total_debits'] = Decimal('0.00')
        context['total_credits'] = Decimal('0.00')
        context['balance_difference'] = Decimal('0.00')
        context['is_balanced'] = True
        context['filtered_accounts_count'] = 0
        context['date_range_display'] = ""

        # Process filters if form is valid
        if filter_form.is_valid():
            # Get filter parameters
            account_type = filter_form.cleaned_data.get('account_type')
            specific_account = filter_form.cleaned_data.get('account')
            start_date = filter_form.cleaned_data.get('start_date')
            end_date = filter_form.cleaned_data.get('end_date')
            show_only_posted = filter_form.cleaned_data.get('show_only_posted', True)

            # Build the base queryset from JournalLine (which contains the actual transactions)
            queryset = JournalLine.objects.select_related(
                'journal_entry', 'account', 'account__account_type'
            ).order_by('journal_entry__date', 'journal_entry__pk', 'pk')

            # Filter by journal entry status
            if show_only_posted:
                queryset = queryset.filter(journal_entry__status=JournalEntry.StatusChoices.POSTED)

            # Filter by date range
            if start_date:
                queryset = queryset.filter(journal_entry__date__gte=start_date)
            if end_date:
                queryset = queryset.filter(journal_entry__date__lte=end_date)

            # Filter by account type
            if account_type:
                queryset = queryset.filter(account__account_type=account_type)

            # Filter by specific account
            if specific_account:
                queryset = queryset.filter(account=specific_account)

            # Convert to transaction list with proper debit/credit display
            transactions = []
            total_debits = Decimal('0.00')
            total_credits = Decimal('0.00')

            for line in queryset:
                transaction = {
                    'date': line.journal_entry.date,
                    'je_id': line.journal_entry.pk,
                    'je_number': f"JE-{line.journal_entry.pk:05d}",
                    'account_code': line.account.code,
                    'account_name': line.account.name,
                    'account_type': line.account.account_type.name if line.account.account_type else '',
                    'description': line.description or line.journal_entry.narration,
                    'reference': getattr(line.journal_entry, 'reference', ''),
                    'debit': line.debit_amount,
                    'credit': line.credit_amount,
                    'entry_type': line.journal_entry.get_entry_type_display(),
                    'status': line.journal_entry.get_status_display(),
                    'created_by': getattr(line.journal_entry.created_by, 'get_full_name', lambda: 'System')() if line.journal_entry.created_by else 'System'
                }
                transactions.append(transaction)
                total_debits += line.debit_amount
                total_credits += line.credit_amount

            context['transactions'] = transactions
            context['total_debits'] = total_debits
            context['total_credits'] = total_credits
            context['balance_difference'] = total_debits - total_credits
            context['is_balanced'] = total_debits == total_credits

            # Get count of unique accounts in the filtered results
            unique_accounts = queryset.values('account').distinct().count()
            context['filtered_accounts_count'] = unique_accounts

            # Build date range display
            if start_date and end_date:
                context['date_range_display'] = f"{start_date.strftime('%d %b %Y')} to {end_date.strftime('%d %b %Y')}"
            elif end_date:
                context['date_range_display'] = f"Up to {end_date.strftime('%d %b %Y')}"
            elif start_date:
                context['date_range_display'] = f"From {start_date.strftime('%d %b %Y')} onwards"

        return context